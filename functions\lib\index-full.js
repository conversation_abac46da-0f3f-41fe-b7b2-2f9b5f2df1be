"use strict";
var __createBinding = (this && this.__createBinding) || (Object.create ? (function(o, m, k, k2) {
    if (k2 === undefined) k2 = k;
    var desc = Object.getOwnPropertyDescriptor(m, k);
    if (!desc || ("get" in desc ? !m.__esModule : desc.writable || desc.configurable)) {
      desc = { enumerable: true, get: function() { return m[k]; } };
    }
    Object.defineProperty(o, k2, desc);
}) : (function(o, m, k, k2) {
    if (k2 === undefined) k2 = k;
    o[k2] = m[k];
}));
var __setModuleDefault = (this && this.__setModuleDefault) || (Object.create ? (function(o, v) {
    Object.defineProperty(o, "default", { enumerable: true, value: v });
}) : function(o, v) {
    o["default"] = v;
});
var __importStar = (this && this.__importStar) || (function () {
    var ownKeys = function(o) {
        ownKeys = Object.getOwnPropertyNames || function (o) {
            var ar = [];
            for (var k in o) if (Object.prototype.hasOwnProperty.call(o, k)) ar[ar.length] = k;
            return ar;
        };
        return ownKeys(o);
    };
    return function (mod) {
        if (mod && mod.__esModule) return mod;
        var result = {};
        if (mod != null) for (var k = ownKeys(mod), i = 0; i < k.length; i++) if (k[i] !== "default") __createBinding(result, mod, k[i]);
        __setModuleDefault(result, mod);
        return result;
    };
})();
var __importDefault = (this && this.__importDefault) || function (mod) {
    return (mod && mod.__esModule) ? mod : { "default": mod };
};
Object.defineProperty(exports, "__esModule", { value: true });
exports.getStripeConnectOnboardingLink = exports.createStripeConnectAccount = exports.getSellerPendingPayouts = exports.getStripeConnectAccountStatus = exports.getWalletData = exports.getListingById = exports.getListings = exports.fixAdminUser = exports.autoReleaseFunds = exports.analyzeShippoTracking = exports.markDeliveryCompleted = exports.configureWalletSettings = exports.getWalletSettings = exports.verifyAdminPin = exports.setAdminPin = exports.releaseFundsWithCode = exports.releaseEscrowWithCode = exports.testEssential = exports.essentialWebhook = exports.adminManageTransaction = exports.adminManageUser = exports.adminFetchDashboardStats = exports.adminBanUser = void 0;
// Minimal functions index - only essential webhook functionality
const functions = __importStar(require("firebase-functions/v1"));
const admin = __importStar(require("firebase-admin"));
const crypto_1 = require("crypto");
const cors_1 = __importDefault(require("cors"));
// Initialize Firebase Admin
admin.initializeApp();
// CORS configuration for development and production
const corsHandler = (0, cors_1.default)({
    origin: [
        'https://h1c1-798a8.web.app',
        'https://h1c1-798a8.firebaseapp.com',
        'https://hivecampus.app',
        'https://www.hivecampus.app',
        'http://localhost:5173',
        'http://localhost:5174',
        'http://localhost:3000',
        'http://localhost:5000'
    ],
    credentials: true,
    methods: ['GET', 'POST', 'PUT', 'DELETE', 'OPTIONS'],
    allowedHeaders: ['Content-Type', 'Authorization', 'X-Requested-With']
});
// Import simple admin notification functions
// export * from './simple-admin-notifications'; // Temporarily disabled to fix deployment timeout
// Import notification functions
// export * from './notifications'; // Temporarily disabled to fix deployment timeout
// Import broadcast notification functions
// export * from './broadcast-notifications'; // Temporarily disabled due to deployment issues
console.log('🚀 Firebase Functions loading...');
// Helper function to generate 6-digit secret code
function generateSecretCode() {
    return Math.floor(100000 + Math.random() * 900000).toString();
}
// ===== ADMIN FUNCTIONS =====
// Admin function to ban/suspend users
exports.adminBanUser = functions
    .runWith({
    memory: '256MB',
    timeoutSeconds: 60,
})
    .https.onCall(async (data, context) => {
    var _a;
    try {
        // Verify admin authentication
        if (!context.auth) {
            throw new functions.https.HttpsError('unauthenticated', 'User must be authenticated');
        }
        // Verify admin role
        const adminDoc = await admin.firestore().collection('users').doc(context.auth.uid).get();
        if (!adminDoc.exists || ((_a = adminDoc.data()) === null || _a === void 0 ? void 0 : _a.role) !== 'admin') {
            throw new functions.https.HttpsError('permission-denied', 'Only admins can ban users');
        }
        const { userId, reason, action = 'ban', duration } = data;
        if (!userId || !reason) {
            throw new functions.https.HttpsError('invalid-argument', 'User ID and reason are required');
        }
        // Update user status
        const updateData = {
            status: action === 'ban' ? 'banned' : 'suspended',
            bannedAt: admin.firestore.Timestamp.now(),
            bannedBy: context.auth.uid,
            banReason: reason,
            updatedAt: admin.firestore.Timestamp.now()
        };
        if (duration && action === 'suspend') {
            updateData.suspendedUntil = admin.firestore.Timestamp.fromDate(new Date(Date.now() + duration * 24 * 60 * 60 * 1000));
        }
        await admin.firestore().collection('users').doc(userId).update(updateData);
        // Log admin action
        await admin.firestore().collection('adminLogs').add({
            adminId: context.auth.uid,
            action: `user_${action}`,
            targetUserId: userId,
            reason,
            timestamp: admin.firestore.Timestamp.now(),
            metadata: { duration }
        });
        // Disable user's Firebase Auth account
        if (action === 'ban') {
            await admin.auth().updateUser(userId, { disabled: true });
        }
        return {
            success: true,
            message: `User ${action}ned successfully`,
            userId
        };
    }
    catch (error) {
        console.error('Error in adminBanUser:', error);
        throw new functions.https.HttpsError('internal', 'Failed to ban user', error);
    }
});
// Admin function to fetch comprehensive dashboard stats
exports.adminFetchDashboardStats = functions
    .runWith({
    memory: '512MB',
    timeoutSeconds: 120,
})
    .https.onCall(async (data, context) => {
    var _a;
    try {
        // Verify admin authentication
        if (!context.auth) {
            throw new functions.https.HttpsError('unauthenticated', 'User must be authenticated');
        }
        // Verify admin role
        const adminDoc = await admin.firestore().collection('users').doc(context.auth.uid).get();
        if (!adminDoc.exists || ((_a = adminDoc.data()) === null || _a === void 0 ? void 0 : _a.role) !== 'admin') {
            throw new functions.https.HttpsError('permission-denied', 'Only admins can access dashboard stats');
        }
        // Fetch all collections in parallel
        const [usersSnapshot, listingsSnapshot, ordersSnapshot, reportsSnapshot, walletsSnapshot] = await Promise.all([
            admin.firestore().collection('users').get(),
            admin.firestore().collection('listings').get(),
            admin.firestore().collection('orders').get(),
            admin.firestore().collection('reports').where('status', '==', 'pending').get(),
            admin.firestore().collection('wallets').get()
        ]);
        // Calculate user metrics
        const users = usersSnapshot.docs.map(doc => doc.data());
        const totalUsers = users.length;
        const activeUsers = users.filter(user => user.status === 'active').length;
        const bannedUsers = users.filter(user => user.status === 'banned').length;
        const verifiedUsers = users.filter(user => user.emailVerified === true).length;
        // Calculate listing metrics
        const listings = listingsSnapshot.docs.map(doc => doc.data());
        const totalListings = listings.length;
        const activeListings = listings.filter(listing => listing.status === 'active').length;
        const pendingListings = listings.filter(listing => listing.status === 'pending').length;
        // Calculate order metrics
        const orders = ordersSnapshot.docs.map(doc => doc.data());
        const totalOrders = orders.length;
        const completedOrders = orders.filter(order => order.status === 'completed').length;
        const totalRevenue = orders
            .filter(order => order.status === 'completed')
            .reduce((sum, order) => sum + (order.totalAmount || 0), 0);
        // Calculate wallet metrics
        const wallets = walletsSnapshot.docs.map(doc => doc.data());
        const totalWalletBalance = wallets.reduce((sum, wallet) => sum + (wallet.balance || 0), 0);
        // Calculate growth metrics (last 30 days)
        const thirtyDaysAgo = new Date(Date.now() - 30 * 24 * 60 * 60 * 1000);
        const recentUsers = users.filter(user => user.createdAt && user.createdAt.toDate() > thirtyDaysAgo).length;
        const recentListings = listings.filter(listing => listing.createdAt && listing.createdAt.toDate() > thirtyDaysAgo).length;
        return {
            success: true,
            data: {
                users: {
                    total: totalUsers,
                    active: activeUsers,
                    banned: bannedUsers,
                    verified: verifiedUsers,
                    recentGrowth: recentUsers
                },
                listings: {
                    total: totalListings,
                    active: activeListings,
                    pending: pendingListings,
                    recentGrowth: recentListings
                },
                orders: {
                    total: totalOrders,
                    completed: completedOrders,
                    revenue: totalRevenue,
                    averageOrderValue: totalOrders > 0 ? totalRevenue / totalOrders : 0
                },
                wallet: {
                    totalBalance: totalWalletBalance,
                    activeWallets: wallets.filter(w => w.balance > 0).length
                },
                reports: {
                    pending: reportsSnapshot.size
                },
                timestamp: admin.firestore.Timestamp.now()
            }
        };
    }
    catch (error) {
        console.error('Error in adminFetchDashboardStats:', error);
        throw new functions.https.HttpsError('internal', 'Failed to fetch dashboard stats', error);
    }
});
// Admin function for comprehensive user management
exports.adminManageUser = functions
    .runWith({
    memory: '256MB',
    timeoutSeconds: 60,
})
    .https.onCall(async (data, context) => {
    var _a, _b;
    try {
        // Verify admin authentication
        if (!context.auth) {
            throw new functions.https.HttpsError('unauthenticated', 'User must be authenticated');
        }
        // Verify admin role
        const adminDoc = await admin.firestore().collection('users').doc(context.auth.uid).get();
        if (!adminDoc.exists || ((_a = adminDoc.data()) === null || _a === void 0 ? void 0 : _a.role) !== 'admin') {
            throw new functions.https.HttpsError('permission-denied', 'Only admins can manage users');
        }
        const { userId, action, data: actionData } = data;
        if (!userId || !action) {
            throw new functions.https.HttpsError('invalid-argument', 'User ID and action are required');
        }
        let updateData = {
            updatedAt: admin.firestore.Timestamp.now()
        };
        switch (action) {
            case 'verify':
                updateData.emailVerified = true;
                updateData.verifiedAt = admin.firestore.Timestamp.now();
                updateData.verifiedBy = context.auth.uid;
                break;
            case 'unverify':
                updateData.emailVerified = false;
                updateData.verifiedAt = null;
                updateData.verifiedBy = null;
                break;
            case 'activate':
                updateData.status = 'active';
                updateData.activatedAt = admin.firestore.Timestamp.now();
                updateData.activatedBy = context.auth.uid;
                // Re-enable Firebase Auth account
                await admin.auth().updateUser(userId, { disabled: false });
                break;
            case 'deactivate':
                updateData.status = 'inactive';
                updateData.deactivatedAt = admin.firestore.Timestamp.now();
                updateData.deactivatedBy = context.auth.uid;
                updateData.deactivationReason = (actionData === null || actionData === void 0 ? void 0 : actionData.reason) || 'Admin action';
                break;
            case 'update_role':
                if (!(actionData === null || actionData === void 0 ? void 0 : actionData.role)) {
                    throw new functions.https.HttpsError('invalid-argument', 'Role is required for role update');
                }
                updateData.role = actionData.role;
                updateData.roleUpdatedAt = admin.firestore.Timestamp.now();
                updateData.roleUpdatedBy = context.auth.uid;
                // Update custom claims
                await admin.auth().setCustomUserClaims(userId, {
                    role: actionData.role,
                    admin: actionData.role === 'admin'
                });
                break;
            case 'add_note':
                if (!(actionData === null || actionData === void 0 ? void 0 : actionData.note)) {
                    throw new functions.https.HttpsError('invalid-argument', 'Note is required');
                }
                // Add to admin notes array
                const userDoc = await admin.firestore().collection('users').doc(userId).get();
                const currentNotes = ((_b = userDoc.data()) === null || _b === void 0 ? void 0 : _b.adminNotes) || [];
                updateData.adminNotes = [
                    ...currentNotes,
                    {
                        note: actionData.note,
                        addedBy: context.auth.uid,
                        addedAt: admin.firestore.Timestamp.now()
                    }
                ];
                break;
            default:
                throw new functions.https.HttpsError('invalid-argument', `Unknown action: ${action}`);
        }
        // Update user document
        await admin.firestore().collection('users').doc(userId).update(updateData);
        // Log admin action
        await admin.firestore().collection('adminLogs').add({
            adminId: context.auth.uid,
            action: `user_${action}`,
            targetUserId: userId,
            timestamp: admin.firestore.Timestamp.now(),
            metadata: actionData || {}
        });
        return {
            success: true,
            message: `User ${action} completed successfully`,
            userId
        };
    }
    catch (error) {
        console.error('Error in adminManageUser:', error);
        throw new functions.https.HttpsError('internal', `Failed to ${data.action} user`, error);
    }
});
// Admin function for transaction management
exports.adminManageTransaction = functions
    .runWith({
    memory: '256MB',
    timeoutSeconds: 60,
})
    .https.onCall(async (data, context) => {
    var _a;
    try {
        // Verify admin authentication
        if (!context.auth) {
            throw new functions.https.HttpsError('unauthenticated', 'User must be authenticated');
        }
        // Verify admin role
        const adminDoc = await admin.firestore().collection('users').doc(context.auth.uid).get();
        if (!adminDoc.exists || ((_a = adminDoc.data()) === null || _a === void 0 ? void 0 : _a.role) !== 'admin') {
            throw new functions.https.HttpsError('permission-denied', 'Only admins can manage transactions');
        }
        const { transactionId, action, reason } = data;
        if (!transactionId || !action) {
            throw new functions.https.HttpsError('invalid-argument', 'Transaction ID and action are required');
        }
        // Get transaction document
        const transactionDoc = await admin.firestore().collection('orders').doc(transactionId).get();
        if (!transactionDoc.exists) {
            throw new functions.https.HttpsError('not-found', 'Transaction not found');
        }
        const transaction = transactionDoc.data();
        let updateData = {
            updatedAt: admin.firestore.Timestamp.now()
        };
        switch (action) {
            case 'refund':
                updateData.status = 'refunded';
                updateData.refundedAt = admin.firestore.Timestamp.now();
                updateData.refundedBy = context.auth.uid;
                updateData.refundReason = reason || 'Admin refund';
                break;
            case 'release_escrow':
                updateData.status = 'completed';
                updateData.escrowReleasedAt = admin.firestore.Timestamp.now();
                updateData.escrowReleasedBy = context.auth.uid;
                updateData.adminReleaseReason = reason || 'Admin release';
                break;
            case 'hold_escrow':
                updateData.escrowHeld = true;
                updateData.escrowHeldAt = admin.firestore.Timestamp.now();
                updateData.escrowHeldBy = context.auth.uid;
                updateData.escrowHoldReason = reason || 'Admin hold';
                break;
            case 'dispute':
                updateData.status = 'disputed';
                updateData.disputedAt = admin.firestore.Timestamp.now();
                updateData.disputedBy = context.auth.uid;
                updateData.disputeReason = reason || 'Admin dispute';
                break;
            default:
                throw new functions.https.HttpsError('invalid-argument', `Unknown action: ${action}`);
        }
        // Update transaction
        await admin.firestore().collection('orders').doc(transactionId).update(updateData);
        // Log admin action
        await admin.firestore().collection('adminLogs').add({
            adminId: context.auth.uid,
            action: `transaction_${action}`,
            targetTransactionId: transactionId,
            reason,
            timestamp: admin.firestore.Timestamp.now(),
            metadata: { originalStatus: transaction === null || transaction === void 0 ? void 0 : transaction.status }
        });
        return {
            success: true,
            message: `Transaction ${action} completed successfully`,
            transactionId
        };
    }
    catch (error) {
        console.error('Error in adminManageTransaction:', error);
        throw new functions.https.HttpsError('internal', `Failed to ${data.action} transaction`, error);
    }
});
// Essential Stripe webhook - only handles payment completion
exports.essentialWebhook = functions
    .runWith({
    memory: '256MB',
    timeoutSeconds: 60,
})
    .https.onRequest(async (req, res) => {
    var _a, _b, _c;
    try {
        console.log('🔗 Essential webhook received');
        if (req.method !== 'POST') {
            res.status(405).send('Method not allowed');
            return;
        }
        const event = req.body;
        console.log(`📨 Event type: ${event.type}`);
        // Handle checkout session completed
        if (event.type === 'checkout.session.completed') {
            const session = event.data.object;
            const metadata = session.metadata;
            if (metadata === null || metadata === void 0 ? void 0 : metadata.orderId) {
                const orderId = metadata.orderId;
                console.log(`📦 Processing order: ${orderId}`);
                // Get order
                const orderRef = admin.firestore().collection('orders').doc(orderId);
                const orderDoc = await orderRef.get();
                if (orderDoc.exists) {
                    const orderData = orderDoc.data();
                    // Generate secret code
                    const secretCode = generateSecretCode();
                    console.log(`🔐 Generated code: ${secretCode}`);
                    // Update order
                    await orderRef.update({
                        status: 'payment_succeeded',
                        secretCode: secretCode,
                        paymentCompletedAt: admin.firestore.Timestamp.now(),
                        updatedAt: admin.firestore.Timestamp.now()
                    });
                    // Update listing to sold
                    if (orderData === null || orderData === void 0 ? void 0 : orderData.listingId) {
                        await admin.firestore().collection('listings').doc(orderData.listingId).update({
                            status: 'sold',
                            soldAt: admin.firestore.Timestamp.now(),
                            updatedAt: admin.firestore.Timestamp.now()
                        });
                        console.log(`✅ Listing ${orderData.listingId} marked as sold`);
                    }
                    // Send buyer notification using new notification system
                    if (orderData === null || orderData === void 0 ? void 0 : orderData.buyerId) {
                        try {
                            // Create in-app notification directly (since we're in the backend)
                            await admin.firestore().collection(`users/${orderData.buyerId}/notifications`).add({
                                type: 'payment_success',
                                title: 'Payment Successful!',
                                message: `Your payment has been processed. Secret code: ${secretCode}`,
                                icon: '/icons/icon-192.png',
                                createdAt: admin.firestore.Timestamp.now(),
                                read: false,
                                link: `/orders/${orderId}`,
                                priority: 'high',
                                actionRequired: false,
                                metadata: {},
                                orderId: orderId,
                                secretCode: secretCode,
                                amount: orderData.totalAmount
                            });
                            // Also send push notification if user has FCM token
                            const tokenDoc = await admin.firestore()
                                .doc(`users/${orderData.buyerId}/fcmTokens/web`)
                                .get();
                            if (tokenDoc.exists && ((_a = tokenDoc.data()) === null || _a === void 0 ? void 0 : _a.active) && ((_b = tokenDoc.data()) === null || _b === void 0 ? void 0 : _b.token)) {
                                const payload = {
                                    token: tokenDoc.data().token,
                                    notification: {
                                        title: 'Payment Successful!',
                                        body: `Your payment has been processed. Secret code: ${secretCode}`,
                                        imageUrl: '/icons/icon-192.png'
                                    },
                                    data: {
                                        type: 'payment_success',
                                        link: `/orders/${orderId}`,
                                        orderId: orderId,
                                        requireInteraction: 'true'
                                    },
                                    webpush: {
                                        headers: {
                                            TTL: '86400',
                                            Urgency: 'high'
                                        },
                                        notification: {
                                            icon: '/icons/icon-192.png',
                                            badge: '/icons/icon-96.png',
                                            tag: 'payment_success',
                                            requireInteraction: true,
                                            actions: [
                                                { action: 'view', title: 'View Order' },
                                                { action: 'dismiss', title: 'Dismiss' }
                                            ],
                                            vibrate: [200, 100, 200]
                                        }
                                    }
                                };
                                try {
                                    await admin.messaging().send(payload);
                                    console.log(`Push notification sent for payment success to user ${orderData.buyerId}`);
                                }
                                catch (pushError) {
                                    console.error('Error sending push notification:', pushError);
                                    // Mark token as inactive if it's invalid
                                    if (pushError.code === 'messaging/registration-token-not-registered') {
                                        await admin.firestore()
                                            .doc(`users/${orderData.buyerId}/fcmTokens/web`)
                                            .update({ active: false });
                                    }
                                }
                            }
                        }
                        catch (notificationError) {
                            console.error('Error sending payment success notification:', notificationError);
                            // Don't fail the webhook if notification fails
                        }
                    }
                    // Create admin notification for payment completion - temporarily disabled
                    // try {
                    //   if (orderData) {
                    //     // Get user data for better notification
                    //     const buyerDoc = await admin.firestore().collection('users').doc(orderData.buyerId).get();
                    //     const buyerData = buyerDoc.exists ? buyerDoc.data() : null;
                    //     const buyerName = buyerData?.name || buyerData?.displayName || buyerData?.email?.split('@')[0] || 'User';
                    //     await createAdminNotification(
                    //       'payment_completed',
                    //       'Payment Completed',
                    //       `${buyerName} completed payment of $${orderData.totalAmount}`,
                    //       {
                    //         userId: orderData.buyerId,
                    //         username: buyerName,
                    //         orderId: orderId,
                    //         amount: orderData.totalAmount,
                    //         metadata: {
                    //           secretCode: secretCode,
                    //           listingId: orderData.listingId,
                    //           sellerId: orderData.sellerId
                    //         },
                    //         actionUrl: `/admin/transactions?search=${orderId}`
                    //       }
                    //     );
                    //   }
                    // } catch (notificationError) {
                    //   console.error('Error creating admin notification:', notificationError);
                    //   // Don't fail the webhook if notification fails
                    // }
                    console.log(`✅ Order ${orderId} processed successfully`);
                }
            }
        }
        // Handle payment failures
        if (event.type === 'payment_intent.payment_failed') {
            const paymentIntent = event.data.object;
            const metadata = paymentIntent.metadata;
            if (metadata === null || metadata === void 0 ? void 0 : metadata.orderId) {
                const orderId = metadata.orderId;
                console.log(`❌ Payment failed for order: ${orderId}`);
                try {
                    // Get order
                    const orderRef = admin.firestore().collection('orders').doc(orderId);
                    const orderDoc = await orderRef.get();
                    if (orderDoc.exists) {
                        const orderData = orderDoc.data();
                        if (orderData) {
                            // Update order status
                            await orderRef.update({
                                status: 'payment_failed',
                                paymentFailedAt: admin.firestore.Timestamp.now(),
                                updatedAt: admin.firestore.Timestamp.now()
                            });
                            // Get user data for notification
                            const buyerDoc = await admin.firestore().collection('users').doc(orderData.buyerId).get();
                            const buyerData = buyerDoc.exists ? buyerDoc.data() : null;
                            const buyerName = (buyerData === null || buyerData === void 0 ? void 0 : buyerData.name) || (buyerData === null || buyerData === void 0 ? void 0 : buyerData.displayName) || ((_c = buyerData === null || buyerData === void 0 ? void 0 : buyerData.email) === null || _c === void 0 ? void 0 : _c.split('@')[0]) || 'User';
                            // Create admin notification for payment failure - temporarily disabled
                            // await createAdminNotification(
                            //   'payment_failed',
                            //   'Payment Failed',
                            //   `Payment failed for ${buyerName}'s order of $${orderData.totalAmount}`,
                            //   {
                            //     userId: orderData.buyerId,
                            //     username: buyerName,
                            //     orderId: orderId,
                            //     amount: orderData.totalAmount,
                            //     metadata: {
                            //       failureReason: paymentIntent.last_payment_error?.message || 'Unknown error',
                            //       listingId: orderData.listingId,
                            //       sellerId: orderData.sellerId
                            //     },
                            //     actionUrl: `/admin/transactions?search=${orderId}`
                            //   }
                            // );
                            console.log(`❌ Payment failure processed for order: ${orderId}`);
                        }
                    }
                }
                catch (error) {
                    console.error('Error processing payment failure:', error);
                }
            }
        }
        res.status(200).json({ received: true });
    }
    catch (error) {
        console.error('❌ Webhook error:', error);
        res.status(500).send('Webhook failed');
    }
});
// Test function
exports.testEssential = functions
    .https.onRequest(async (_req, res) => {
    res.json({
        success: true,
        message: 'Essential webhook working',
        testCode: generateSecretCode(),
        timestamp: new Date().toISOString()
    });
});
// Release funds with secret code (alias for compatibility)
exports.releaseEscrowWithCode = functions
    .https.onCall(async (data, context) => {
    var _a;
    try {
        if (!context.auth) {
            throw new functions.https.HttpsError('unauthenticated', 'Authentication required');
        }
        const { orderId, secretCode } = data;
        if (!orderId || !secretCode) {
            throw new functions.https.HttpsError('invalid-argument', 'Order ID and secret code required');
        }
        // Get order
        const orderRef = admin.firestore().collection('orders').doc(orderId);
        const orderDoc = await orderRef.get();
        if (!orderDoc.exists) {
            throw new functions.https.HttpsError('not-found', 'Order not found');
        }
        const orderData = orderDoc.data();
        // Verify buyer
        if ((orderData === null || orderData === void 0 ? void 0 : orderData.buyerId) !== context.auth.uid) {
            throw new functions.https.HttpsError('permission-denied', 'Not authorized');
        }
        // Verify secret code
        if ((orderData === null || orderData === void 0 ? void 0 : orderData.secretCode) !== secretCode) {
            throw new functions.https.HttpsError('invalid-argument', 'Invalid secret code');
        }
        // Update order status
        await orderRef.update({
            status: 'completed',
            fundsReleased: true,
            fundsReleasedAt: admin.firestore.Timestamp.now(),
            completedAt: admin.firestore.Timestamp.now(),
            updatedAt: admin.firestore.Timestamp.now()
        });
        // Update listing to sold (if not already sold)
        if (orderData === null || orderData === void 0 ? void 0 : orderData.listingId) {
            const listingRef = admin.firestore().collection('listings').doc(orderData.listingId);
            const listingDoc = await listingRef.get();
            if (listingDoc.exists && ((_a = listingDoc.data()) === null || _a === void 0 ? void 0 : _a.status) !== 'sold') {
                await listingRef.update({
                    status: 'sold',
                    soldAt: admin.firestore.Timestamp.now(),
                    updatedAt: admin.firestore.Timestamp.now()
                });
                console.log(`✅ Listing ${orderData.listingId} marked as sold via secret code`);
            }
        }
        console.log(`✅ Funds released for order: ${orderId}`);
        return {
            success: true,
            message: 'Funds released successfully'
        };
    }
    catch (error) {
        console.error('Error releasing funds:', error);
        throw error;
    }
});
// Release funds with secret code (new name)
exports.releaseFundsWithCode = functions
    .https.onCall(async (data, context) => {
    var _a;
    try {
        if (!context.auth) {
            throw new functions.https.HttpsError('unauthenticated', 'Authentication required');
        }
        const { orderId, secretCode } = data;
        if (!orderId || !secretCode) {
            throw new functions.https.HttpsError('invalid-argument', 'Order ID and secret code required');
        }
        // Get order
        const orderRef = admin.firestore().collection('orders').doc(orderId);
        const orderDoc = await orderRef.get();
        if (!orderDoc.exists) {
            throw new functions.https.HttpsError('not-found', 'Order not found');
        }
        const orderData = orderDoc.data();
        // Verify buyer
        if ((orderData === null || orderData === void 0 ? void 0 : orderData.buyerId) !== context.auth.uid) {
            throw new functions.https.HttpsError('permission-denied', 'Not authorized');
        }
        // Verify secret code
        if ((orderData === null || orderData === void 0 ? void 0 : orderData.secretCode) !== secretCode) {
            throw new functions.https.HttpsError('invalid-argument', 'Invalid secret code');
        }
        // Update order status
        await orderRef.update({
            status: 'completed',
            fundsReleased: true,
            fundsReleasedAt: admin.firestore.Timestamp.now(),
            completedAt: admin.firestore.Timestamp.now(),
            updatedAt: admin.firestore.Timestamp.now()
        });
        // Update listing to sold (if not already sold)
        if (orderData === null || orderData === void 0 ? void 0 : orderData.listingId) {
            const listingRef = admin.firestore().collection('listings').doc(orderData.listingId);
            const listingDoc = await listingRef.get();
            if (listingDoc.exists && ((_a = listingDoc.data()) === null || _a === void 0 ? void 0 : _a.status) !== 'sold') {
                await listingRef.update({
                    status: 'sold',
                    soldAt: admin.firestore.Timestamp.now(),
                    updatedAt: admin.firestore.Timestamp.now()
                });
                console.log(`✅ Listing ${orderData.listingId} marked as sold via secret code`);
            }
        }
        console.log(`✅ Funds released for order: ${orderId}`);
        return {
            success: true,
            message: 'Funds released successfully'
        };
    }
    catch (error) {
        console.error('Error releasing funds:', error);
        throw error;
    }
});
// Function to set admin PIN
exports.setAdminPin = functions.https.onCall(async (data, context) => {
    var _a;
    try {
        // Verify the user is authenticated and is an admin
        if (!context.auth) {
            throw new functions.https.HttpsError('unauthenticated', 'User must be authenticated');
        }
        const { pin } = data;
        if (!pin || pin.length !== 8 || !/^\d{8}$/.test(pin)) {
            throw new functions.https.HttpsError('invalid-argument', 'PIN must be exactly 8 digits');
        }
        // Verify user is admin
        const userDoc = await admin.firestore().collection('users').doc(context.auth.uid).get();
        if (!userDoc.exists || ((_a = userDoc.data()) === null || _a === void 0 ? void 0 : _a.role) !== 'admin') {
            throw new functions.https.HttpsError('permission-denied', 'Only admin users can set PIN');
        }
        // Hash the PIN for security
        const hashedPin = (0, crypto_1.createHash)('sha256').update(pin).digest('hex');
        // Store the hashed PIN in admin settings
        await admin.firestore().collection('adminSettings').doc('security').set({
            adminPin: hashedPin,
            pinSetAt: admin.firestore.Timestamp.now(),
            pinSetBy: context.auth.uid
        }, { merge: true });
        console.log(`Admin PIN set by user: ${context.auth.uid}`);
        return {
            success: true,
            message: 'Admin PIN set successfully'
        };
    }
    catch (error) {
        console.error('Error setting admin PIN:', error);
        throw new functions.https.HttpsError('internal', 'Failed to set admin PIN', error);
    }
});
// Function to verify admin PIN
exports.verifyAdminPin = functions
    .runWith({
    memory: '256MB',
    timeoutSeconds: 60
})
    .https.onCall(async (data, context) => {
    var _a, _b, _c;
    try {
        // Verify the user is authenticated and is an admin
        if (!context.auth) {
            throw new functions.https.HttpsError('unauthenticated', 'User must be authenticated');
        }
        const { pin } = data;
        if (!pin || pin.length !== 8 || !/^\d{8}$/.test(pin)) {
            throw new functions.https.HttpsError('invalid-argument', 'PIN must be exactly 8 digits');
        }
        // Verify user is admin
        const userDoc = await admin.firestore().collection('users').doc(context.auth.uid).get();
        if (!userDoc.exists || ((_a = userDoc.data()) === null || _a === void 0 ? void 0 : _a.role) !== 'admin') {
            throw new functions.https.HttpsError('permission-denied', 'Only admin users can verify PIN');
        }
        // Get stored PIN hash
        const securityDoc = await admin.firestore().collection('adminSettings').doc('security').get();
        if (!securityDoc.exists || !((_b = securityDoc.data()) === null || _b === void 0 ? void 0 : _b.adminPin)) {
            throw new functions.https.HttpsError('not-found', 'Admin PIN not set. Please set up your PIN first.');
        }
        // Hash the provided PIN and compare
        const hashedPin = (0, crypto_1.createHash)('sha256').update(pin).digest('hex');
        const storedPin = (_c = securityDoc.data()) === null || _c === void 0 ? void 0 : _c.adminPin;
        if (hashedPin !== storedPin) {
            throw new functions.https.HttpsError('permission-denied', 'wrong pin unauthorised entry');
        }
        // Update last access time
        await admin.firestore().collection('users').doc(context.auth.uid).update({
            lastAdminAccess: admin.firestore.Timestamp.now()
        });
        console.log(`Admin PIN verified for user: ${context.auth.uid}`);
        return {
            success: true,
            message: 'PIN verified successfully'
        };
    }
    catch (error) {
        console.error('Error verifying admin PIN:', error);
        throw error;
    }
});
// Helper function to get wallet configuration
async function getWalletConfig() {
    const settingsDoc = await admin.firestore().collection('adminSettings').doc('wallet').get();
    if (settingsDoc.exists) {
        const data = settingsDoc.data();
        return {
            signupBonus: (data === null || data === void 0 ? void 0 : data.signupBonus) || 0,
            referralBonus: (data === null || data === void 0 ? void 0 : data.referralBonus) || 0,
            enableSignupBonus: (data === null || data === void 0 ? void 0 : data.enableSignupBonus) || false,
            enableReferralBonus: (data === null || data === void 0 ? void 0 : data.enableReferralBonus) || false
        };
    }
    return {
        signupBonus: 0,
        referralBonus: 0,
        enableSignupBonus: false,
        enableReferralBonus: false
    };
}
// Get wallet settings (admin only)
exports.getWalletSettings = functions
    .runWith({
    memory: '256MB',
    timeoutSeconds: 30,
})
    .https.onCall(async (data, context) => {
    var _a;
    try {
        // Verify authentication
        if (!context.auth) {
            throw new functions.https.HttpsError('unauthenticated', 'Authentication required');
        }
        // Verify admin role
        const userDoc = await admin.firestore().collection('users').doc(context.auth.uid).get();
        if (!userDoc.exists || ((_a = userDoc.data()) === null || _a === void 0 ? void 0 : _a.role) !== 'admin') {
            throw new functions.https.HttpsError('permission-denied', 'Admin access required');
        }
        const settings = await getWalletConfig();
        return {
            success: true,
            settings
        };
    }
    catch (error) {
        console.error('Error in getWalletSettings:', error);
        const errorMessage = error instanceof Error ? error.message : 'Unknown error';
        throw new functions.https.HttpsError('internal', errorMessage);
    }
});
// Configure wallet settings (admin only)
exports.configureWalletSettings = functions
    .runWith({
    memory: '256MB',
    timeoutSeconds: 30,
})
    .https.onCall(async (data, context) => {
    var _a;
    try {
        // Verify authentication
        if (!context.auth) {
            throw new functions.https.HttpsError('unauthenticated', 'Authentication required');
        }
        // Verify admin role
        const userDoc = await admin.firestore().collection('users').doc(context.auth.uid).get();
        if (!userDoc.exists || ((_a = userDoc.data()) === null || _a === void 0 ? void 0 : _a.role) !== 'admin') {
            throw new functions.https.HttpsError('permission-denied', 'Admin access required');
        }
        const { signupBonus, referralBonus, enableSignupBonus, enableReferralBonus } = data;
        // Validate input
        if (typeof signupBonus !== 'number' || signupBonus < 0 || signupBonus > 100) {
            throw new functions.https.HttpsError('invalid-argument', 'Signup bonus must be between 0 and 100');
        }
        if (typeof referralBonus !== 'number' || referralBonus < 0 || referralBonus > 100) {
            throw new functions.https.HttpsError('invalid-argument', 'Referral bonus must be between 0 and 100');
        }
        // Update settings
        await admin.firestore().collection('adminSettings').doc('wallet').set({
            signupBonus,
            referralBonus,
            enableSignupBonus: Boolean(enableSignupBonus),
            enableReferralBonus: Boolean(enableReferralBonus),
            updatedAt: admin.firestore.Timestamp.now(),
            updatedBy: context.auth.uid
        }, { merge: true });
        console.log(`Wallet settings updated by admin: ${context.auth.uid}`);
        return {
            success: true,
            message: 'Wallet settings updated successfully'
        };
    }
    catch (error) {
        console.error('Error configuring wallet settings:', error);
        const errorMessage = error instanceof Error ? error.message : 'Unknown error';
        throw new functions.https.HttpsError('internal', errorMessage);
    }
});
// Mark delivery as completed (for sellers)
exports.markDeliveryCompleted = functions.https.onCall(async (data, context) => {
    try {
        if (!context.auth) {
            throw new functions.https.HttpsError('unauthenticated', 'Authentication required');
        }
        const { orderId } = data;
        if (!orderId) {
            throw new functions.https.HttpsError('invalid-argument', 'Order ID is required');
        }
        // Get order
        const orderRef = admin.firestore().collection('orders').doc(orderId);
        const orderDoc = await orderRef.get();
        if (!orderDoc.exists) {
            throw new functions.https.HttpsError('not-found', 'Order not found');
        }
        const orderData = orderDoc.data();
        // Verify seller
        if ((orderData === null || orderData === void 0 ? void 0 : orderData.sellerId) !== context.auth.uid) {
            throw new functions.https.HttpsError('permission-denied', 'Only the seller can mark delivery as completed');
        }
        // Verify order status
        if ((orderData === null || orderData === void 0 ? void 0 : orderData.status) !== 'payment_succeeded') {
            throw new functions.https.HttpsError('failed-precondition', 'Order must be in payment_succeeded status');
        }
        // Update order status to delivered and start 3-day countdown
        const deliveryDate = admin.firestore.Timestamp.now();
        const autoReleaseDate = admin.firestore.Timestamp.fromDate(new Date(Date.now() + 3 * 24 * 60 * 60 * 1000) // 3 days from now
        );
        await orderRef.update({
            status: 'delivered',
            deliveredAt: deliveryDate,
            autoReleaseDate: autoReleaseDate,
            deliveryMethod: 'in_person', // Seller manually marked as delivered
            updatedAt: admin.firestore.Timestamp.now()
        });
        // Send notification to buyer
        try {
            await admin.firestore().collection('notifications').add({
                userId: orderData.buyerId,
                type: 'delivery_confirmed',
                title: '📦 Item Delivered!',
                message: `The seller has marked your order "${orderData.title || orderData.listingTitle}" as delivered. Please enter your secret code to release payment.`,
                data: {
                    orderId,
                    secretCode: orderData.secretCode,
                    autoReleaseDate: autoReleaseDate.toDate().toISOString(),
                    showCodeEntry: true
                },
                priority: 'high',
                read: false,
                createdAt: admin.firestore.Timestamp.now()
            });
            console.log(`📬 Delivery notification sent to buyer: ${orderData.buyerId}`);
        }
        catch (notificationError) {
            console.error('Error sending delivery notification:', notificationError);
            // Don't fail the delivery marking if notification fails
        }
        console.log(`✅ Order ${orderId} marked as delivered by seller`);
        return {
            success: true,
            message: 'Delivery marked as completed. Buyer has 3 days to confirm receipt.',
            autoReleaseDate: autoReleaseDate.toDate().toISOString()
        };
    }
    catch (error) {
        console.error('Error marking delivery as completed:', error);
        throw error;
    }
});
// Automatic Shippo tracking analysis (scheduled function)
exports.analyzeShippoTracking = functions.pubsub.schedule('every 2 hours').onRun(async () => {
    try {
        console.log('🔍 Starting Shippo tracking analysis...');
        // Get all orders with tracking that haven't been delivered yet
        const ordersQuery = await admin.firestore()
            .collection('orders')
            .where('status', '==', 'payment_succeeded')
            .where('trackingInfo.trackingNumber', '!=', null)
            .get();
        const promises = [];
        ordersQuery.docs.forEach((doc) => {
            var _a;
            const orderData = doc.data();
            const orderId = doc.id;
            if ((_a = orderData.trackingInfo) === null || _a === void 0 ? void 0 : _a.trackingNumber) {
                promises.push(checkShippoDeliveryStatus(orderId, orderData));
            }
        });
        await Promise.all(promises);
        console.log(`✅ Analyzed ${ordersQuery.docs.length} orders with tracking`);
    }
    catch (error) {
        console.error('❌ Error in Shippo tracking analysis:', error);
    }
});
// Helper function to check individual order delivery status
async function checkShippoDeliveryStatus(orderId, orderData) {
    try {
        const trackingNumber = orderData.trackingInfo.trackingNumber;
        // Mock Shippo API call (replace with actual Shippo API)
        // const shippoResponse = await fetch(`https://api.goshippo.com/tracks/${trackingNumber}`, {
        //   headers: { 'Authorization': `ShippoToken ${SHIPPO_API_KEY}` }
        // });
        // const trackingData = await shippoResponse.json();
        // For now, simulate delivery detection
        // In production, check: trackingData.tracking_status.status === 'DELIVERED'
        const isDelivered = false; // Replace with actual Shippo check
        if (isDelivered) {
            console.log(`📦 Shippo detected delivery for order: ${orderId}`);
            // Update order status to delivered and start 3-day countdown
            const deliveryDate = admin.firestore.Timestamp.now();
            const autoReleaseDate = admin.firestore.Timestamp.fromDate(new Date(Date.now() + 3 * 24 * 60 * 60 * 1000) // 3 days from now
            );
            await admin.firestore().collection('orders').doc(orderId).update({
                status: 'delivered',
                deliveredAt: deliveryDate,
                autoReleaseDate: autoReleaseDate,
                deliveryMethod: 'mail', // Detected via Shippo tracking
                updatedAt: admin.firestore.Timestamp.now()
            });
            // Send notification to buyer
            await admin.firestore().collection('notifications').add({
                userId: orderData.buyerId,
                type: 'delivery_detected',
                title: '📦 Package Delivered!',
                message: `Your order "${orderData.title || orderData.listingTitle}" has been delivered according to tracking. Please enter your secret code to release payment.`,
                data: {
                    orderId,
                    secretCode: orderData.secretCode,
                    autoReleaseDate: autoReleaseDate.toDate().toISOString(),
                    showCodeEntry: true,
                    trackingNumber
                },
                priority: 'high',
                read: false,
                createdAt: admin.firestore.Timestamp.now()
            });
            console.log(`📬 Auto-delivery notification sent for order: ${orderId}`);
        }
    }
    catch (error) {
        console.error(`❌ Error checking delivery for order ${orderId}:`, error);
    }
}
// Auto-release funds after 3 days (scheduled function)
exports.autoReleaseFunds = functions.pubsub.schedule('every 1 hours').onRun(async () => {
    try {
        console.log('⏰ Starting auto-release check...');
        const now = admin.firestore.Timestamp.now();
        // Find orders eligible for auto-release (delivered > 3 days ago)
        const ordersQuery = await admin.firestore()
            .collection('orders')
            .where('status', '==', 'delivered')
            .where('autoReleaseDate', '<=', now)
            .get();
        const promises = [];
        ordersQuery.docs.forEach((doc) => {
            const orderData = doc.data();
            const orderId = doc.id;
            // Only auto-release if funds haven't been released yet
            if (!orderData.fundsReleased && !orderData.completedAt) {
                promises.push(autoReleaseOrderFunds(orderId, orderData));
            }
        });
        await Promise.all(promises);
        console.log(`✅ Auto-released ${promises.length} orders`);
    }
    catch (error) {
        console.error('❌ Error in auto-release:', error);
    }
});
// Helper function to auto-release funds for a specific order
async function autoReleaseOrderFunds(orderId, orderData) {
    var _a;
    try {
        console.log(`💰 Auto-releasing funds for order: ${orderId}`);
        // Update order status to completed
        await admin.firestore().collection('orders').doc(orderId).update({
            status: 'completed',
            fundsReleased: true,
            fundsReleasedAt: admin.firestore.Timestamp.now(),
            completedAt: admin.firestore.Timestamp.now(),
            autoReleased: true, // Flag to indicate this was auto-released
            updatedAt: admin.firestore.Timestamp.now()
        });
        // Update listing to sold (if not already sold)
        if (orderData === null || orderData === void 0 ? void 0 : orderData.listingId) {
            const listingRef = admin.firestore().collection('listings').doc(orderData.listingId);
            const listingDoc = await listingRef.get();
            if (listingDoc.exists && ((_a = listingDoc.data()) === null || _a === void 0 ? void 0 : _a.status) !== 'sold') {
                await listingRef.update({
                    status: 'sold',
                    soldAt: admin.firestore.Timestamp.now(),
                    updatedAt: admin.firestore.Timestamp.now()
                });
                console.log(`✅ Listing ${orderData.listingId} marked as sold (auto-release)`);
            }
        }
        // Send notifications to both buyer and seller
        const notifications = [];
        // Notify buyer
        notifications.push(admin.firestore().collection('notifications').add({
            userId: orderData.buyerId,
            type: 'funds_auto_released',
            title: '⏰ Funds Auto-Released',
            message: `Funds for your order "${orderData.title || orderData.listingTitle}" have been automatically released to the seller after 3 days.`,
            data: { orderId },
            priority: 'medium',
            read: false,
            createdAt: admin.firestore.Timestamp.now()
        }));
        // Notify seller
        notifications.push(admin.firestore().collection('notifications').add({
            userId: orderData.sellerId,
            type: 'funds_received',
            title: '💰 Payment Received',
            message: `You have received payment for "${orderData.title || orderData.listingTitle}" (auto-released after 3 days).`,
            data: { orderId },
            priority: 'high',
            read: false,
            createdAt: admin.firestore.Timestamp.now()
        }));
        await Promise.all(notifications);
        console.log(`📬 Auto-release notifications sent for order: ${orderId}`);
    }
    catch (error) {
        console.error(`❌ Error auto-releasing order ${orderId}:`, error);
    }
}
// Fix admin user function (for setup)
exports.fixAdminUser = functions.https.onCall(async (data, _context) => {
    try {
        const { email } = data;
        if (!email) {
            throw new functions.https.HttpsError('invalid-argument', 'Email is required');
        }
        // Get user by email
        const userRecord = await admin.auth().getUserByEmail(email);
        // Set custom claims with both admin and role
        await admin.auth().setCustomUserClaims(userRecord.uid, {
            admin: true,
            role: 'admin'
        });
        // Update or create user profile in Firestore with complete admin setup
        await admin.firestore().collection('users').doc(userRecord.uid).set({
            uid: userRecord.uid,
            name: userRecord.displayName || 'Admin User',
            email: userRecord.email,
            role: 'admin',
            university: 'Hive Campus Admin',
            createdAt: admin.firestore.Timestamp.now(),
            updatedAt: admin.firestore.Timestamp.now(),
            emailVerified: true,
            status: 'active',
            adminLevel: 'super',
            permissions: ['user_management', 'content_moderation', 'analytics', 'system_settings', 'super_admin']
        }, { merge: true });
        console.log(`Admin user fixed for: ${email}`);
        return {
            success: true,
            message: `Admin user fixed for ${email}`,
            uid: userRecord.uid
        };
    }
    catch (error) {
        console.error('Error fixing admin user:', error);
        throw new functions.https.HttpsError('internal', 'Failed to fix admin user', error);
    }
});
// Helper function to verify authentication
function verifyAuth(context) {
    if (!context.auth) {
        throw new functions.https.HttpsError('unauthenticated', 'Authentication required');
    }
    return context.auth;
}
// Get listings with filtering
exports.getListings = functions.https.onCall(async (data, context) => {
    var _a;
    try {
        verifyAuth(context);
        const { university, category, type, condition, minPrice, maxPrice, ownerId, status = 'active', limit = 20, lastVisible } = data;
        // Get current user's university for visibility filtering
        const userDoc = await admin.firestore().collection('users').doc(context.auth.uid).get();
        const currentUserUniversity = (_a = userDoc.data()) === null || _a === void 0 ? void 0 : _a.university;
        let query = admin.firestore().collection('listings')
            .where('status', '==', status)
            .orderBy('createdAt', 'desc');
        // Apply filters if provided
        if (university) {
            query = query.where('university', '==', university);
        }
        if (category) {
            query = query.where('category', '==', category);
        }
        if (type) {
            query = query.where('type', '==', type);
        }
        if (condition) {
            query = query.where('condition', '==', condition);
        }
        if (ownerId) {
            query = query.where('ownerId', '==', ownerId);
        }
        // Apply limit
        query = query.limit(limit);
        // Apply pagination if lastVisible is provided
        if (lastVisible) {
            const lastDoc = await admin.firestore().collection('listings').doc(lastVisible).get();
            if (lastDoc.exists) {
                query = query.startAfter(lastDoc);
            }
        }
        const snapshot = await query.get();
        const listings = [];
        let lastVisibleId = null;
        snapshot.forEach(doc => {
            const listing = doc.data();
            // Apply visibility filtering
            const isVisible = listing.visibility === 'public' ||
                (listing.visibility === 'university' && listing.university === currentUserUniversity);
            if (!isVisible) {
                return; // Skip this listing
            }
            // Apply price filtering in memory (can't do range queries along with other filters in Firestore)
            const price = listing.price;
            if ((minPrice === undefined || price >= minPrice) &&
                (maxPrice === undefined || price <= maxPrice)) {
                listings.push(Object.assign({ id: doc.id }, listing));
            }
            // Set the last visible document ID for pagination
            lastVisibleId = doc.id;
        });
        return {
            success: true,
            data: {
                listings,
                lastVisible: lastVisibleId,
                total: listings.length
            }
        };
    }
    catch (error) {
        console.error('Error getting listings:', error);
        throw new functions.https.HttpsError('internal', error instanceof Error ? error.message : 'Unknown error');
    }
});
// Get a single listing by ID
exports.getListingById = functions.https.onCall(async (data, context) => {
    var _a;
    try {
        verifyAuth(context);
        const { listingId } = data;
        if (!listingId) {
            throw new functions.https.HttpsError('invalid-argument', 'Listing ID is required');
        }
        const listingDoc = await admin.firestore().collection('listings').doc(listingId).get();
        if (!listingDoc.exists) {
            throw new functions.https.HttpsError('not-found', 'Listing not found');
        }
        const listing = listingDoc.data();
        // Don't return deleted listings unless it's the owner
        if ((listing === null || listing === void 0 ? void 0 : listing.status) === 'deleted' && listing.ownerId !== ((_a = context.auth) === null || _a === void 0 ? void 0 : _a.uid)) {
            throw new functions.https.HttpsError('not-found', 'Listing not found');
        }
        return {
            success: true,
            data: Object.assign({ id: listingDoc.id }, listing)
        };
    }
    catch (error) {
        console.error('Error getting listing by ID:', error);
        throw error;
    }
});
// Get wallet balance and history
exports.getWalletData = functions.https.onCall(async (_data, context) => {
    try {
        verifyAuth(context);
        const userId = context.auth.uid;
        const walletDoc = await admin.firestore().collection('wallets').doc(userId).get();
        if (!walletDoc.exists) {
            // Initialize wallet if it doesn't exist
            const walletData = {
                userId,
                balance: 0,
                referralCode: `user${userId.substring(0, 6)}`,
                usedReferral: false,
                history: [],
                grantedBy: 'system',
                createdAt: admin.firestore.Timestamp.now(),
                lastUpdated: admin.firestore.Timestamp.now()
            };
            await admin.firestore().collection('wallets').doc(userId).set(walletData);
            return walletData;
        }
        return walletDoc.data();
    }
    catch (error) {
        console.error('Error getting wallet data:', error);
        throw new functions.https.HttpsError('internal', error instanceof Error ? error.message : 'Unknown error');
    }
});
// Get Stripe Connect account status
exports.getStripeConnectAccountStatus = functions
    .runWith({
    memory: '256MB',
    timeoutSeconds: 30,
})
    .https.onCall(async (data, context) => {
    try {
        // Check authentication
        if (!context.auth) {
            throw new functions.https.HttpsError('unauthenticated', 'User must be authenticated');
        }
        const userId = context.auth.uid;
        console.log('Fetching Stripe Connect account status for user:', userId);
        // Get connect account from Firestore
        const connectAccountDoc = await admin.firestore().collection('connectAccounts').doc(userId).get();
        if (!connectAccountDoc.exists) {
            console.log(`No connect account found for user: ${userId}`);
            return null;
        }
        const connectAccount = connectAccountDoc.data();
        console.log('Connect account data:', connectAccount);
        return {
            accountId: (connectAccount === null || connectAccount === void 0 ? void 0 : connectAccount.stripeAccountId) || null,
            onboardingUrl: (connectAccount === null || connectAccount === void 0 ? void 0 : connectAccount.onboardingUrl) || null,
            dashboardUrl: (connectAccount === null || connectAccount === void 0 ? void 0 : connectAccount.dashboardUrl) || null,
            isOnboarded: (connectAccount === null || connectAccount === void 0 ? void 0 : connectAccount.isOnboarded) || false,
            chargesEnabled: (connectAccount === null || connectAccount === void 0 ? void 0 : connectAccount.chargesEnabled) || false,
            payoutsEnabled: (connectAccount === null || connectAccount === void 0 ? void 0 : connectAccount.payoutsEnabled) || false,
        };
    }
    catch (error) {
        console.error('Error in getStripeConnectAccountStatus:', error);
        const errorMessage = error instanceof Error ? error.message : 'Unknown error';
        throw new functions.https.HttpsError('internal', errorMessage);
    }
});
// Get pending payouts for seller
exports.getSellerPendingPayouts = functions
    .runWith({
    memory: '256MB',
    timeoutSeconds: 30,
})
    .https.onCall(async (data, context) => {
    try {
        // Check authentication
        if (!context.auth) {
            throw new functions.https.HttpsError('unauthenticated', 'User must be authenticated');
        }
        const userId = context.auth.uid;
        console.log('Fetching pending payouts for user:', userId);
        let pendingPayouts = [];
        try {
            // Get orders where this user is the seller and payment has succeeded but funds not released
            const ordersQuery = await admin.firestore()
                .collection('orders')
                .where('sellerId', '==', userId)
                .where('status', 'in', ['payment_succeeded', 'delivered'])
                .where('fundsReleased', '==', false)
                .get();
            console.log(`Found ${ordersQuery.docs.length} pending orders for seller ${userId}`);
            pendingPayouts = ordersQuery.docs.map(doc => {
                const orderData = doc.data();
                const totalAmount = orderData.totalAmount || 0;
                // Calculate commission (8% for textbooks, 10% for others, $0.50 flat fee for items $1-$5)
                let commissionAmount = 0;
                if (totalAmount <= 5) {
                    commissionAmount = 0.50;
                }
                else {
                    const commissionRate = orderData.category === 'textbooks' ? 0.08 : 0.10;
                    commissionAmount = totalAmount * commissionRate;
                }
                const sellerAmount = Math.max(0, totalAmount - commissionAmount);
                return {
                    orderId: doc.id,
                    amount: totalAmount,
                    commissionAmount: commissionAmount,
                    sellerAmount: sellerAmount,
                    paymentIntentId: orderData.paymentIntentId || null,
                    createdAt: orderData.createdAt || orderData.paymentCompletedAt,
                    status: orderData.status,
                    listingTitle: orderData.listingTitle || orderData.title,
                    buyerName: orderData.buyerName || 'Anonymous'
                };
            });
            console.log(`Processed ${pendingPayouts.length} pending payouts`);
        }
        catch (firestoreError) {
            console.error('Error querying Firestore for pending payouts:', firestoreError);
            // Return empty array instead of throwing error
            pendingPayouts = [];
        }
        return pendingPayouts;
    }
    catch (error) {
        console.error('Error in getSellerPendingPayouts:', error);
        const errorMessage = error instanceof Error ? error.message : 'Unknown error';
        throw new functions.https.HttpsError('internal', errorMessage);
    }
});
// Create Stripe Connect account
exports.createStripeConnectAccount = functions
    .runWith({
    memory: '256MB',
    timeoutSeconds: 60,
})
    .https.onCall(async (data, context) => {
    try {
        // Check authentication
        if (!context.auth) {
            throw new functions.https.HttpsError('unauthenticated', 'User must be authenticated');
        }
        const { accountType } = data;
        const userId = context.auth.uid;
        if (!accountType || !['student', 'merchant'].includes(accountType)) {
            throw new functions.https.HttpsError('invalid-argument', 'Valid accountType is required');
        }
        console.log(`Creating Stripe Connect account for user ${userId}, type: ${accountType}`);
        // Check if user already has a connect account
        const existingAccountDoc = await admin.firestore().collection('connectAccounts').doc(userId).get();
        if (existingAccountDoc.exists) {
            const existingAccount = existingAccountDoc.data();
            console.log('User already has a connect account:', existingAccount === null || existingAccount === void 0 ? void 0 : existingAccount.stripeAccountId);
            return {
                accountId: existingAccount === null || existingAccount === void 0 ? void 0 : existingAccount.stripeAccountId,
                onboardingUrl: (existingAccount === null || existingAccount === void 0 ? void 0 : existingAccount.onboardingUrl) || null,
                message: 'Account already exists'
            };
        }
        // Get user data
        const userDoc = await admin.firestore().collection('users').doc(userId).get();
        if (!userDoc.exists) {
            throw new functions.https.HttpsError('not-found', 'User profile not found');
        }
        const userData = userDoc.data();
        // For now, create a placeholder connect account record
        // In production, this would create an actual Stripe Connect account
        const mockAccountId = `acct_mock_${userId.substring(0, 8)}`;
        const mockOnboardingUrl = `https://connect.stripe.com/setup/s/${mockAccountId}`;
        // Store the Connect account in Firestore
        await admin.firestore().collection('connectAccounts').doc(userId).set({
            userId: userId,
            stripeAccountId: mockAccountId,
            accountType: accountType,
            isOnboarded: false,
            chargesEnabled: false,
            payoutsEnabled: false,
            detailsSubmitted: false,
            onboardingUrl: mockOnboardingUrl,
            createdAt: admin.firestore.Timestamp.now(),
            updatedAt: admin.firestore.Timestamp.now(),
        });
        console.log(`Created mock Stripe Connect account ${mockAccountId} for user ${userId}`);
        return {
            accountId: mockAccountId,
            onboardingUrl: mockOnboardingUrl,
            message: 'Connect account created successfully'
        };
    }
    catch (error) {
        console.error('Error in createStripeConnectAccount:', error);
        const errorMessage = error instanceof Error ? error.message : 'Unknown error';
        throw new functions.https.HttpsError('internal', errorMessage);
    }
});
// Get Stripe Connect onboarding link
exports.getStripeConnectOnboardingLink = functions
    .runWith({
    memory: '256MB',
    timeoutSeconds: 30,
})
    .https.onCall(async (data, context) => {
    try {
        // Check authentication
        if (!context.auth) {
            throw new functions.https.HttpsError('unauthenticated', 'User must be authenticated');
        }
        const userId = context.auth.uid;
        const { refreshUrl, returnUrl } = data;
        console.log(`Getting onboarding link for user ${userId}`);
        // Get connect account from Firestore
        const connectAccountDoc = await admin.firestore().collection('connectAccounts').doc(userId).get();
        if (!connectAccountDoc.exists) {
            throw new functions.https.HttpsError('not-found', 'No Stripe Connect account found. Please create an account first.');
        }
        const connectAccount = connectAccountDoc.data();
        // If already onboarded, return dashboard URL instead
        if (connectAccount === null || connectAccount === void 0 ? void 0 : connectAccount.isOnboarded) {
            return {
                onboardingUrl: connectAccount.dashboardUrl || connectAccount.onboardingUrl,
                isOnboarded: true
            };
        }
        // Return existing onboarding URL or create a new one
        const onboardingUrl = (connectAccount === null || connectAccount === void 0 ? void 0 : connectAccount.onboardingUrl) || `https://connect.stripe.com/setup/s/${connectAccount === null || connectAccount === void 0 ? void 0 : connectAccount.stripeAccountId}`;
        return {
            onboardingUrl: onboardingUrl,
            isOnboarded: false
        };
    }
    catch (error) {
        console.error('Error in getStripeConnectOnboardingLink:', error);
        const errorMessage = error instanceof Error ? error.message : 'Unknown error';
        throw new functions.https.HttpsError('internal', errorMessage);
    }
});
