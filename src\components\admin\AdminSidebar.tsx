import React, { useState } from 'react';
import { NavLink, useNavigate } from 'react-router-dom';
import {
  BarChart3,
  Users,
  GraduationCap,
  Package,
  CreditCard,
  MessageSquare,
  Truck,
  TrendingUp,
  Flag,
  Activity,
  Settings,
  Wallet,
  BarChart,
  LogOut,
  X,
  Send
} from 'lucide-react';
import BroadcastModal from './BroadcastModal';
import { useAuth } from '../../hooks/useAuth';
import { logOut } from '../../firebase/auth';

interface AdminSidebarProps {
  isOpen: boolean;
  onClose: () => void;
}

const AdminSidebar: React.FC<AdminSidebarProps> = ({ isOpen, onClose }) => {
  const { userProfile } = useAuth();
  const navigate = useNavigate();
  const [showBroadcastModal, setShowBroadcastModal] = useState(false);

  const handleLogout = async () => {
    try {
      await logOut();
      navigate('/');
    } catch (error) {
      console.error('Error logging out:', error);
    }
  };

  const navigationItems = [
    { name: 'Overview', href: '/admin', icon: BarChart3 },
    { name: 'Users', href: '/admin/users', icon: Users },
    { name: 'Universities', href: '/admin/universities', icon: GraduationCap },
    { name: 'Listings', href: '/admin/listings', icon: Package },
    { name: 'Transactions', href: '/admin/transactions', icon: CreditCard },
    { name: 'System Health', href: '/admin/health', icon: Activity },
    { name: 'Chat & Messaging', href: '/admin/chat', icon: MessageSquare },
    { name: 'Shipping', href: '/admin/shipping', icon: Truck },
    { name: 'Analytics', href: '/admin/analytics', icon: TrendingUp },
    { name: 'Reports', href: '/admin/reports', icon: Flag },
    { name: 'ReeFlex', href: '/admin/reeflex', icon: Activity },
    { name: 'Wallet Settings', href: '/admin/wallet-settings', icon: Wallet },
    { name: 'Wallet Reports', href: '/admin/wallet-reports', icon: BarChart },
    { name: 'Settings', href: '/admin/settings', icon: Settings },
  ];

  return (
    <>
      {/* Desktop Sidebar */}
      <div className="hidden lg:fixed lg:inset-y-0 lg:flex lg:w-64 lg:flex-col">
        <div className="flex min-h-0 flex-1 flex-col bg-white dark:bg-gray-800 border-r border-gray-200 dark:border-gray-700">
          {/* Logo */}
          <div className="flex h-16 flex-shrink-0 items-center px-4 border-b border-gray-200 dark:border-gray-700">
            <img
              src="/hive-campus-logo.svg"
              alt="Hive Campus"
              className="h-8 w-8"
            />
            <div className="ml-3">
              <h1 className="text-lg font-semibold text-gray-900 dark:text-white">
                Admin Panel
              </h1>
              <p className="text-xs text-gray-500 dark:text-gray-400">
                Hive Campus
              </p>
            </div>
          </div>

          {/* Navigation */}
          <nav className="flex-1 space-y-1 px-2 py-4">
            {navigationItems.map((item) => (
              <NavLink
                key={item.name}
                to={item.href}
                className={({ isActive }) =>
                  `group flex items-center px-2 py-2 text-sm font-medium rounded-md transition-colors ${
                    isActive
                      ? 'bg-blue-50 dark:bg-blue-900/20 text-blue-700 dark:text-blue-300'
                      : 'text-gray-600 dark:text-gray-300 hover:bg-gray-50 dark:hover:bg-gray-700 hover:text-gray-900 dark:hover:text-white'
                  }`
                }
              >
                <item.icon className="mr-3 h-5 w-5 flex-shrink-0" />
                {item.name}
              </NavLink>
            ))}
          </nav>

          {/* Broadcast Notification Button */}
          <div className="px-2 pb-4">
            <button
              onClick={() => setShowBroadcastModal(true)}
              className="w-full flex items-center px-2 py-2 text-sm font-medium text-white bg-primary-600 hover:bg-primary-700 rounded-md transition-colors"
            >
              <Send className="mr-3 h-5 w-5 flex-shrink-0" />
              Send Notification
            </button>
          </div>

          {/* User Profile & Logout */}
          <div className="flex-shrink-0 border-t border-gray-200 dark:border-gray-700 p-4">
            <div className="flex items-center">
              <div className="flex-shrink-0">
                <img
                  className="h-8 w-8 rounded-full bg-gray-300"
                  src={userProfile?.profilePictureURL || '/placeholder-avatar.svg'}
                  alt="Admin"
                  onError={(e) => {
                    const target = e.target as HTMLImageElement;
                    target.src = '/placeholder-avatar.svg';
                  }}
                />
              </div>
              <div className="ml-3 flex-1">
                <p className="text-sm font-medium text-gray-700 dark:text-gray-200">
                  {userProfile?.name || 'Admin'}
                </p>
                <p className="text-xs text-gray-500 dark:text-gray-400">
                  Administrator
                </p>
              </div>
              <button
                onClick={handleLogout}
                className="ml-3 flex-shrink-0 p-1 text-gray-400 hover:text-gray-500 dark:hover:text-gray-300"
                title="Logout"
              >
                <LogOut className="h-5 w-5" />
              </button>
            </div>
          </div>
        </div>
      </div>

      {/* Mobile Sidebar */}
      <div className={`lg:hidden fixed inset-0 z-50 ${isOpen ? 'block' : 'hidden'}`}>
        <div className="fixed inset-0 bg-gray-600 bg-opacity-75" onClick={onClose}></div>
        <div className="fixed inset-y-0 left-0 flex w-64 flex-col bg-white dark:bg-gray-800">
          {/* Mobile Header */}
          <div className="flex h-16 items-center justify-between px-4 border-b border-gray-200 dark:border-gray-700">
            <div className="flex items-center">
              <img
                src="/hive-campus-logo.svg"
                alt="Hive Campus"
                className="h-8 w-8"
              />
              <h1 className="ml-3 text-lg font-semibold text-gray-900 dark:text-white">
                Admin Panel
              </h1>
            </div>
            <button
              onClick={onClose}
              className="text-gray-400 hover:text-gray-500 dark:hover:text-gray-300"
            >
              <X className="h-6 w-6" />
            </button>
          </div>

          {/* Mobile Navigation */}
          <nav className="flex-1 space-y-1 px-2 py-4">
            {navigationItems.map((item) => (
              <NavLink
                key={item.name}
                to={item.href}
                onClick={onClose}
                className={({ isActive }) =>
                  `group flex items-center px-2 py-2 text-sm font-medium rounded-md transition-colors ${
                    isActive
                      ? 'bg-blue-50 dark:bg-blue-900/20 text-blue-700 dark:text-blue-300'
                      : 'text-gray-600 dark:text-gray-300 hover:bg-gray-50 dark:hover:bg-gray-700 hover:text-gray-900 dark:hover:text-white'
                  }`
                }
              >
                <item.icon className="mr-3 h-5 w-5 flex-shrink-0" />
                {item.name}
              </NavLink>
            ))}
          </nav>

          {/* Mobile Broadcast Notification Button */}
          <div className="px-2 pb-4">
            <button
              onClick={() => {
                setShowBroadcastModal(true);
                onClose();
              }}
              className="w-full flex items-center px-2 py-2 text-sm font-medium text-white bg-primary-600 hover:bg-primary-700 rounded-md transition-colors"
            >
              <Send className="mr-3 h-5 w-5 flex-shrink-0" />
              Send Notification
            </button>
          </div>

          {/* Mobile User Profile */}
          <div className="flex-shrink-0 border-t border-gray-200 dark:border-gray-700 p-4">
            <div className="flex items-center">
              <div className="flex-shrink-0">
                <img
                  className="h-8 w-8 rounded-full bg-gray-300"
                  src={userProfile?.profilePictureURL || '/placeholder-avatar.svg'}
                  alt="Admin"
                  onError={(e) => {
                    const target = e.target as HTMLImageElement;
                    target.src = '/placeholder-avatar.svg';
                  }}
                />
              </div>
              <div className="ml-3 flex-1">
                <p className="text-sm font-medium text-gray-700 dark:text-gray-200">
                  {userProfile?.name || 'Admin'}
                </p>
                <p className="text-xs text-gray-500 dark:text-gray-400">
                  Administrator
                </p>
              </div>
              <button
                onClick={handleLogout}
                className="ml-3 flex-shrink-0 p-1 text-gray-400 hover:text-gray-500 dark:hover:text-gray-300"
                title="Logout"
              >
                <LogOut className="h-5 w-5" />
              </button>
            </div>
          </div>
        </div>
      </div>

      {/* Broadcast Modal */}
      <BroadcastModal
        isOpen={showBroadcastModal}
        onClose={() => setShowBroadcastModal(false)}
        adminId={userProfile?.uid || ''}
      />
    </>
  );
};

export default AdminSidebar;
